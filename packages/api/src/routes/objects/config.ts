import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { z } from "zod";

// Valid object types
export const VALID_OBJECT_TYPES = [
	"contact",
	"company",
	"property",
] as const;
export type ObjectType = (typeof VALID_OBJECT_TYPES)[number];

// Mapping from plural/alternative forms to singular forms
const OBJECT_TYPE_MAPPING: Record<string, ObjectType> = {
	// Plural forms (from frontend)
	contacts: "contact",
	companies: "company", 
	properties: "property",
	// Singular forms (keep as-is)
	contact: "contact",
	company: "company",
	property: "property",
};

// Helper function to normalize object type (handles both singular and plural)
export function normalizeObjectType(objectType: string): ObjectType | null {
	const normalized = OBJECT_TYPE_MAPPING[objectType.toLowerCase()];
	return normalized || null;
}

import {
	CompanyCreateSchema,
	CompanyUpdateSchema,
	type CompanyWithRelations,
} from "../companies/types";
// Validation schemas imports
import {
	ContactCreateSchema,
	ContactUpdateSchema,
	type ContactWithRelations,
} from "../contacts/types";
import {
	PropertyCreateSchema,
	PropertyUpdateSchema,
	type PropertyWithRelations,
} from "../properties/types";

// Object configuration mapping
export const OBJECT_CONFIG = {
	contact: {
		tableName: "contact" as const,
		createSchema: ContactCreateSchema,
		updateSchema: ContactUpdateSchema,
		include: {
			company: {
				select: {
					id: true,
					name: true,
					website: true,
					logo: true,
				},
			},
			creator: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
			relatedContacts: true,
			objectTags: {
				include: {
					tag: {
						select: {
							id: true,
							name: true,
							color: true,
						},
					},
					adder: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
			},
		},
		searchFields: [
			"id",
			"firstName",
			"lastName",
			"title",
			"summary",
			"email",
		],
		filterFields: {
			// Basic info
			firstName: "string",
			lastName: "string",
			name: "string",
			title: "string",
			summary: "string",
			image: "string",
			
			// Contact details
			email: "string",
			phone: "string",
			website: "string",
			linkedin: "string",
			facebook: "string",
			twitter: "string",
			instagram: "string",
			
			// Status and categorization
			status: "stringArray",
			persona: "stringArray", 
			stage: "stringArray",
			source: "string",
			
			// Personal details
			spouseName: "string",
			birthday: "dateRange",
			
			// Company relationship
			company: "relation",
			companyId: "string",
			
			// Address fields
			"address.street": "string",
			"address.street2": "string", 
			"address.city": "string",
			"address.state": "string",
			"address.zip": "string",
			"address.country": "string",
			
			// Metadata
			createdAt: "dateRange",
			updatedAt: "dateRange",
			createdBy: "string",
			updatedBy: "string",
		},
		transformResult: (contact: any) => {
			try {
				return {
					id: contact.id,
					name: `${contact.firstName || ""} ${contact.lastName || ""}`.trim(),
					firstName: contact.firstName || "",
					lastName: contact.lastName || "",
					title: contact.title || "",
					image: contact.image || null,
					email: Array.isArray(contact.email)
						? contact.email.map((e: any) => ({
								address: e.value || e.address || "",
								label: e.label || "Work",
								isPrimary: e.isPrimary || false,
								isBad: e.isBad || false,
						  }))
						: [],
					phone: (() => {
						// Handle various phone data structures from MongoDB/Prisma
						const phoneData = contact.phone;
						
						// If phone is null or undefined, return empty array
						if (!phoneData) {
							return [];
						}
						
						// If phone is already a properly formatted array
						if (Array.isArray(phoneData)) {
							return phoneData.map((p: any) => ({
								number: p.number || p.value || "",
								label: p.label || "Work",
								isPrimary: Boolean(p.isPrimary),
								isBad: Boolean(p.isBad),
							}));
						}
						
						// If phone is a JSON string, try to parse it
						if (typeof phoneData === 'string') {
							try {
								const parsed = JSON.parse(phoneData);
								if (Array.isArray(parsed)) {
									return parsed.map((p: any) => ({
										number: p.number || p.value || "",
										label: p.label || "Work",
										isPrimary: Boolean(p.isPrimary),
										isBad: Boolean(p.isBad),
									}));
								}
							} catch (e) {
								console.error("Failed to parse phone JSON:", e);
								return [];
							}
						}
						
						// If phone is a single object, wrap it in an array
						if (typeof phoneData === 'object' && phoneData.number) {
							return [{
								number: phoneData.number || phoneData.value || "",
								label: phoneData.label || "Work",
								isPrimary: Boolean(phoneData.isPrimary),
								isBad: Boolean(phoneData.isBad),
							}];
						}
						
						// Fallback: return empty array
						console.warn("Unexpected phone data structure:", phoneData);
						return [];
					})(),
					address: Array.isArray(contact.address)
						? contact.address.map((a: any) => ({
								street: a.street || "",
								street2: a.street2 || "",
								city: a.city || "",
								state: a.state || "",
								zip: a.zip || "",
								country: a.country || "United States",
								label: a.label || "Work",
								isPrimary: a.isPrimary || false,
						  }))
						: [],
					company: contact.company
						? {
								id: contact.company.id,
								name: contact.company.name,
								website: contact.company.website,
								logo: contact.company.logo,
						  }
						: null,
					creator: contact.creator
						? {
								id: contact.creator.id,
								name: contact.creator.name,
								email: contact.creator.email,
								image: contact.creator.image,
						  }
						: {
								id: "system",
								name: "System",
								email: "",
								image: null,
						  },
					tags: contact.objectTags?.map((ot: any) => ({
						id: ot.tag.id,
						name: ot.tag.name,
						color: ot.tag.color,
					})) || [],
					status: contact.status || null,
					stage: contact.stage || null,
					source: contact.source || null,
					persona: contact.persona || null,
					website: contact.website || null,
					spouseName: contact.spouseName || null,
					birthday: contact.birthday || null,
					summary: contact.summary || null,
					companyId: contact.companyId || null,
					createdAt: contact.createdAt,
					updatedAt: contact.updatedAt,
					createdBy: contact.createdBy,
					updatedBy: contact.updatedBy,
					isDeleted: contact.isDeleted || false,
				};
			} catch (error) {
				logger.error("Contact transformation error:", error, { contact });
				// Fallback: return minimal valid contact data
				return {
					id: contact?.id || "unknown",
					name: contact ? `${contact.firstName || ""} ${contact.lastName || ""}`.trim() || "Unknown Contact" : "Unknown Contact",
					email: [],
					phone: [],
					tags: [],
					status: null,
					stage: null,
					source: null,
					persona: null,
					website: null,
					spouseName: null,
					birthday: null,
					summary: null,
					companyId: null,
					company: null,
					createdAt: contact?.createdAt || new Date(),
					updatedAt: contact?.updatedAt || new Date(),
					createdBy: contact?.createdBy || null,
					updatedBy: contact?.updatedBy || null,
					isDeleted: true, // Mark as deleted so it won't show up in lists
				};
			}
		},
	},
	company: {
		tableName: "company" as const,
		createSchema: CompanyCreateSchema,
		updateSchema: CompanyUpdateSchema,
		include: {
			contacts: {
				select: {
					id: true,
					firstName: true,
					lastName: true,
					email: true,
				},
				where: { isDeleted: false },
			},
			creator: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
			objectTags: {
				include: {
					tag: {
						select: {
							id: true,
							name: true,
							color: true,
						},
					},
					adder: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
			},
		},
		searchFields: ["name", "description", "industry", "website"],
		filterFields: {
			// Basic info
			name: "string",
			description: "string",
			logo: "string",
			
			// Business details
			industry: "stringArray",
			size: "stringArray",
			website: "string",
			
			// Contact details
			email: "string",
			phone: "string",
			
			// Address fields
			"address.street": "string",
			"address.street2": "string",
			"address.city": "string", 
			"address.state": "string",
			"address.zip": "string",
			"address.country": "string",
			
			// Metadata
			createdAt: "dateRange",
			updatedAt: "dateRange",
			createdBy: "string",
			updatedBy: "string",
		},
		transformResult: (company: any) => {
			try {
				return {
					id: company.id,
					name: company.name,
					website: company.website || null,
					industry: company.industry || null,
					size: company.size || null,
					description: company.description || null,
					logo: company.logo || null,
					address: company.address || null,
					phone: company.phone || null,
					email: company.email || null,
					contacts: company.contacts || [],
					tags: company.objectTags?.map((ot: any) => ({
						id: ot.tag.id,
						name: ot.tag.name,
						color: ot.tag.color,
					})) || [],
					creator: company.creator
						? {
								id: company.creator.id,
								name: company.creator.name,
								email: company.creator.email,
								image: company.creator.image,
						  }
						: {
								id: "system",
								name: "System",
								email: "",
								image: null,
						  },
					createdAt: company.createdAt,
					updatedAt: company.updatedAt,
					createdBy: company.createdBy,
					updatedBy: company.updatedBy,
					isDeleted: company.isDeleted || false,
				};
			} catch (error) {
				logger.error("Company transformation error:", error, { company });
				return {
					id: company?.id || "unknown",
					name: company?.name || "Unknown Company",
					tags: [],
					contacts: [],
					createdAt: company?.createdAt || new Date(),
					updatedAt: company?.updatedAt || new Date(),
					createdBy: company?.createdBy || null,
					updatedBy: company?.updatedBy || null,
					isDeleted: true,
				};
			}
		},
	},
	property: {
		tableName: "property" as const,
		createSchema: PropertyCreateSchema,
		updateSchema: PropertyUpdateSchema,
		include: {
			creator: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
			location: true,
			physicalDetails: true,
			financials: true,
			flags: true,
			mlsData: true,
			legalInfo: true,
			demographics: true,
			unitMixes: true,
			saleHistory: true,
			mortgages: true,
			foreclosureInfo: true,
			mlsHistory: true,
			objectTags: {
				include: {
					tag: {
						select: {
							id: true,
							name: true,
							color: true,
						},
					},
					adder: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
			},
		},
		searchFields: ["name", "propertyType", "market", "subMarket"],
		filterFields: {
			// Basic info
			name: "string",
			recordType: "stringArray",
			image: "string",
			
			// Property classification
			propertyType: "stringArray",
			propertySubType: "stringArray",
			market: "string",
			subMarket: "string",
			status: "stringArray",
			listingId: "string",
			
			// Address fields (these will be handled specially for JSON filtering)
			"address.location.street": "string",
			"address.location.city": "string", 
			"address.location.state": "string",
			"address.location.zip": "string",
			"address.location.county": "string",
			"address.location.country": "string",
			"city": "string", // Frontend might send this
			"state": "string", // Frontend might send this
			"zip": "string", // Frontend might send this
			"county": "string",
			"country": "string",
			
			// Financial fields
			price: "number",
			"financials.price": "number",
			"financials.listPrice": "number",
			"financials.askingPrice": "number",
			"financials.salePrice": "number",
			"financials.rentAmount": "number",
			"financials.marketValue": "number",
			"financials.assessedValue": "number",
			"financials.taxAmount": "number",
			"financials.hoaFees": "number",
			"financials.capRate": "number",
			"financials.grossYield": "number",
			"financials.netYield": "number",
			
			// Physical detail fields (these are in the physicalDetails relation)
			bedrooms: "number",
			bathrooms: "number", 
			squareFootage: "number",
			yearBuilt: "number",
			lotSize: "number",
			units: "number",
			"physicalDetails.bedrooms": "number",
			"physicalDetails.bathrooms": "number",
			"physicalDetails.squareFootage": "number", 
			"physicalDetails.yearBuilt": "number",
			"physicalDetails.lotSize": "number",
			"physicalDetails.units": "number",
			"physicalDetails.stories": "number",
			"physicalDetails.parkingSpaces": "number",
			"physicalDetails.garage": "number",
			"physicalDetails.pool": "string",
			"physicalDetails.heatingType": "string",
			"physicalDetails.coolingType": "string",
			"physicalDetails.roofType": "string",
			"physicalDetails.foundationType": "string",
			"physicalDetails.exteriorMaterial": "string",
			"physicalDetails.interiorFeatures": "string",
			"physicalDetails.appliances": "string",
			"physicalDetails.flooring": "string",
			
			// MLS fields
			"mlsData.mlsNumber": "string",
			"mlsData.listingAgent": "string",
			"mlsData.buyerAgent": "string",
			"mlsData.mlsStatus": "stringArray",
			"mlsData.daysOnMarket": "number",
			"mlsData.listDate": "dateRange",
			"mlsData.soldDate": "dateRange",
			"mlsData.pendingDate": "dateRange",
			"mlsData.withdrawnDate": "dateRange",
			
			// Legal fields
			"legalInfo.parcelNumber": "string",
			"legalInfo.legalDescription": "string",
			"legalInfo.zoning": "string", 
			"legalInfo.landUse": "string",
			"legalInfo.propertyRights": "string",
			"legalInfo.easements": "string",
			"legalInfo.restrictions": "string",
			"legalInfo.homeownersAssociation": "string",
			
			// Flags and status
			"flags.isForSale": "string",
			"flags.isForRent": "string", 
			"flags.isSold": "string",
			"flags.isPending": "string",
			"flags.isOffMarket": "string",
			"flags.isDistressed": "string",
			"flags.isForeclosure": "string",
			"flags.isAuction": "string",
			"flags.isReo": "string",
			"flags.isShortSale": "string",
			"flags.isBankOwned": "string",
			"flags.isVacant": "string",
			"flags.isOccupied": "string",
			"flags.isRental": "string",
			"flags.isCommercial": "string",
			"flags.isResidential": "string",
			"flags.isLand": "string",
			"flags.isMultiFamily": "string",
			
			// Demographics
			"demographics.medianIncome": "number",
			"demographics.population": "number",
			"demographics.crimeRate": "number",
			"demographics.walkScore": "number",
			"demographics.schoolRating": "number",
			
			// Metadata
			createdAt: "dateRange",
			updatedAt: "dateRange",
			createdBy: "string", 
			updatedBy: "string",
		},
		transformResult: (property: any) => {
			try {
				return {
					id: property.id,
					name: property.name,
					recordType: property.recordType || "property",
					image: property.image || null,
					propertyType: property.propertyType || null,
					propertySubType: property.propertySubType || null,
					market: property.market || null,
					subMarket: property.subMarket || null,
					listingId: property.listingId || null,
					status: property.status || null,
					// Extract address from location relation for frontend compatibility
					address: property.location?.address || null,
					// Extract physical details to top level for frontend compatibility
					price: property.financials?.price || null,
					bedrooms: property.physicalDetails?.bedrooms || null,
					bathrooms: property.physicalDetails?.bathrooms || null,
					squareFootage: property.physicalDetails?.squareFootage || null,
					yearBuilt: property.physicalDetails?.yearBuilt || null,
					lotSize: property.physicalDetails?.lotSize || null,
					units: property.physicalDetails?.units || null,

					location: property.location || null,
					physicalDetails: property.physicalDetails || null,
					financials: property.financials || null,
					flags: property.flags || null,
					mlsData: property.mlsData || null,
					legalInfo: property.legalInfo || null,
					demographics: property.demographics || null,
					unitMixes: property.unitMixes || [],
					saleHistory: property.saleHistory || [],
					mortgages: property.mortgages || [],
					foreclosureInfo: property.foreclosureInfo || [],
					mlsHistory: property.mlsHistory || [],
					creator: property.creator
						? {
								id: property.creator.id,
								name: property.creator.name,
								email: property.creator.email,
								image: property.creator.image,
						  }
						: {
								id: "system",
								name: "System",
								email: "",
								image: null,
						  },
					createdAt: property.createdAt,
					updatedAt: property.updatedAt,
					createdBy: property.createdBy,
					updatedBy: property.updatedBy,
					isDeleted: property.isDeleted || false,
				};
			} catch (error) {
				logger.error("Property transformation error:", error, { property });
				return {
					id: property?.id || "unknown",
					name: property?.name || "Unknown Property",
					recordType: "property",
					createdAt: property?.createdAt || new Date(),
					updatedAt: property?.updatedAt || new Date(),
					createdBy: property?.createdBy || null,
					updatedBy: property?.updatedBy || null,
					isDeleted: true,
				};
			}
		},
	},
} as const;

// Helper function to get database table
export function getDbTable(objectType: ObjectType) {
	const config = OBJECT_CONFIG[objectType];
	return (db as any)[config.tableName];
}

// Helper function to validate object type
export function validateObjectType(
	objectType: string,
): objectType is ObjectType {
	return VALID_OBJECT_TYPES.includes(objectType as ObjectType);
}

// Helper function to build search conditions
export function buildSearchConditions(
	objectType: ObjectType,
	search: string,
	additionalFilters: Record<string, any> = {},
) {
	const config = OBJECT_CONFIG[objectType];
	const conditions: any = {
		isDeleted: false,
		...additionalFilters,
	};

	if (search) {
		const searchConditions: any[] = [];
		
		config.searchFields.forEach((field) => {
			// Handle JSON fields differently for contacts
			if (objectType === "contact" && field === "email") {
				// For now, skip email search in JSON fields to avoid Prisma errors
				// TODO: Implement proper JSON search when Prisma supports it better
				return;
			}
			
			// Regular text field search
			searchConditions.push({
				[field]: { contains: search, mode: "insensitive" },
			});
		});
		
		if (searchConditions.length > 0) {
			conditions.OR = searchConditions;
		}
	}

	return conditions;
}
