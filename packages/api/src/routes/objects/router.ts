import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	buildSearchConditions,
	getDbTable,
	OBJECT_CONFIG,
	type ObjectType,
	normalizeObjectType,
	validateObjectType,
} from "./config";
import { recordNavigationRouter } from "./record-navigation";
import {
	createRecordCreatedActivity,
	createRecordDeletedActivity,
	createFieldChangeActivity,
	detectFieldChanges,
	createSystemActivity,
} from "../../lib/system-activity";

async function createStatusHistory(
	objectId: string,
	objectType: ObjectType,
	statusField: string,
	fromStatus: string | null,
	toStatus: string,
	userId: string,
	organizationId: string,
) {
	try {
		await db.objectStatusHistory.create({
			data: {
				objectId,
				objectType,
				statusField,
				fromStatus,
				toStatus,
				userId,
				organizationId,
			},
		});
		logger.info(
			`Status history created for ${objectType} ${objectId}: ${statusField} changed from "${fromStatus}" to "${toStatus}"`,
		);
	} catch (error) {
		logger.error("Failed to create status history:", error);
		// Don't throw - we don't want status history creation to fail the main operation
	}
}

// Status fields that should trigger history tracking
const STATUS_FIELDS = ["status", "stage", "propertyStatus"];

function isStatusField(field: string): boolean {
	return STATUS_FIELDS.includes(field);
}

// Define SortOrder type to match Prisma's enum
type SortOrder = 'asc' | 'desc';

export const objectsRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Middleware to validate object type
const validateObjectTypeMiddleware = async (c: any, next: any) => {
	const objectTypeParam = c.req.param("objectType");
	const objectType = normalizeObjectType(objectTypeParam);

	if (!objectType) {
		return c.json({ error: "Invalid object type" }, 400);
	}

	c.set("objectType" as any, objectType);
	await next();
};

// Create object
objectsRouter.post(
	"/objects/:objectType",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;
			const body = await c.req.json();

			const config = OBJECT_CONFIG[objectType];
			const validatedData = config.createSchema.parse(body);

			if (!validatedData.organizationId) {
				return c.json(
					{ error: "Organization ID is required creating object" },
					400,
				);
			}

			await verifyOrganizationMembership(
				validatedData.organizationId,
				user.id,
			);

			let result: any;

			// Handle property creation specially due to separate address/location model
			if (objectType === "property") {
				const { 
					tags,
					lists,
					linkedContacts,
					linkedCompanies,
					tasks,
					
					// Location & Address fields
					address, 
					location, 
					website,
					neighborhood,
					subdivision,
					lotNumber,
					parcelNumber,
					zoning,
					
					// Physical Details fields
					yearBuilt, 
					squareFootage, 
					units, 
					floors,
					structures,
					bedrooms, 
					bathrooms, 
					roomsCount,
					buildingSquareFeet,
					garageSquareFeet,
					livingSquareFeet,
					lotSquareFeet,
					lotSize,
					lotType,
					lotAcres,
					construction,
					primaryUse,
					propertyUse,
					class: propertyClass,
					parking,
					parkingSpaces,
					garageType,
					heatingType,
					meterType,
					legalDescription,
					
					// Financial fields
					price, 
					estimatedValue,
					pricePerSquareFoot,
					equity,
					equityPercent,
					estimatedEquity,
					saleDate,
					salePrice,
					lastSalePrice,
					lastSaleDate,
					landValue,
					buildingValue,
					cap,
					exchange,
					exchangeId,
					taxInfo,
					
					// Boolean Flags
					absenteeOwner,
					inStateAbsenteeOwner,
					outOfStateAbsenteeOwner,
					ownerOccupied,
					corporateOwned,
					vacant,
					mobileHome,
					carport,
					auction,
					cashBuyer,
					investorBuyer,
					freeClear,
					highEquity,
					privateLender,
					deedInLieu,
					quitClaim,
					sheriffsDeed,
					warrantyDeed,
					inherited,
					spousalDeath,
					lien,
					taxLien,
					preForeclosure,
					trusteeSale,
					floodZone,
					
					// MLS Data
					mlsActive,
					mlsCancelled,
					mlsFailed,
					mlsHasPhotos,
					mlsPending,
					mlsSold,
					mlsDaysOnMarket,
					mlsListingPrice,
					mlsListingPricePerSquareFoot,
					mlsSoldPrice,
					mlsStatus,
					mlsType,
					mlsListingDate,
					
					// Legal & Environmental
					floodZoneDescription,
					floodZoneType,
					noticeType,
					reaId,
					lastUpdateDate,
					
					// Demographics
					fmrEfficiency,
					fmrFourBedroom,
					fmrOneBedroom,
					fmrThreeBedroom,
					fmrTwoBedroom,
					fmrYear,
					hudAreaCode,
					hudAreaName,
					medianIncome,
					suggestedRent,
					
					// REA API data
					reaApiData,
					
					...propertyData 
				} = validatedData as any;
				
				// Helper function to extract and merge REA API data with form data
				const extractReaData = (reaData: any) => {
					if (!reaData) return {};
					
					const result: any = {};
					
					// Extract from propertyInfo
					if (reaData.propertyInfo) {
						const propInfo = reaData.propertyInfo;
						result.yearBuilt = propInfo.yearBuilt || yearBuilt;
						result.buildingSquareFeet = propInfo.buildingSquareFeet || buildingSquareFeet;
						result.livingSquareFeet = propInfo.livingSquareFeet || livingSquareFeet;
						result.lotSquareFeet = propInfo.lotSquareFeet || lotSquareFeet;
						result.garageSquareFeet = propInfo.garageSquareFeet || garageSquareFeet;
						result.bathrooms = propInfo.bathrooms || bathrooms;
						result.bedrooms = propInfo.bedrooms || bedrooms;
						result.roomsCount = propInfo.roomsCount || roomsCount;
						result.floors = propInfo.stories || floors;
						result.units = propInfo.unitsCount || units;
						result.parkingSpaces = propInfo.parkingSpaces || parkingSpaces;
						result.construction = propInfo.construction || construction;
						result.propertyUse = propInfo.propertyUse || propertyUse;
						result.pricePerSquareFoot = propInfo.pricePerSquareFoot || pricePerSquareFoot;
						result.carport = propInfo.carport || carport;
					}
					
					// Extract from lotInfo
					if (reaData.lotInfo) {
						const lotInfo = reaData.lotInfo;
						result.lotAcres = parseFloat(lotInfo.lotAcres) || lotAcres;
						result.lotSquareFeet = lotInfo.lotSquareFeet || result.lotSquareFeet || lotSquareFeet;
						result.zoning = lotInfo.zoning || zoning;
						result.legalDescription = lotInfo.legalDescription || legalDescription;
						result.lotNumber = lotInfo.lotNumber || lotNumber;
						result.parcelNumber = lotInfo.apn || parcelNumber;
						result.class = lotInfo.propertyClass || propertyClass;
					}
					
					// Extract from demographics
					if (reaData.demographics) {
						const demo = reaData.demographics;
						result.fmrEfficiency = parseFloat(demo.fmrEfficiency) || fmrEfficiency;
						result.fmrFourBedroom = parseFloat(demo.fmrFourBedroom) || fmrFourBedroom;
						result.fmrOneBedroom = parseFloat(demo.fmrOneBedroom) || fmrOneBedroom;
						result.fmrThreeBedroom = parseFloat(demo.fmrThreeBedroom) || fmrThreeBedroom;
						result.fmrTwoBedroom = parseFloat(demo.fmrTwoBedroom) || fmrTwoBedroom;
						result.fmrYear = parseFloat(demo.fmrYear) || fmrYear;
						result.hudAreaCode = demo.hudAreaCode || hudAreaCode;
						result.hudAreaName = demo.hudAreaName || hudAreaName;
						result.medianIncome = parseFloat(demo.medianIncome) || medianIncome;
						result.suggestedRent = demo.suggestedRent || suggestedRent;
					}
					
					// Extract from taxInfo
					if (reaData.taxInfo) {
						const tax = reaData.taxInfo;
						result.estimatedValue = tax.assessedValue || tax.marketValue || estimatedValue;
						result.landValue = tax.assessedLandValue || tax.marketLandValue || landValue;
						result.buildingValue = tax.assessedImprovementValue || tax.marketImprovementValue || buildingValue;
						result.taxInfo = reaData.taxInfo; // Store full tax info as JSON
					}
					
					return result;
				};

				// Extract and merge REA data
				const reaData = extractReaData(reaApiData);
				
				// Create the property first
				result = await db.property.create({
					data: {
						...propertyData,
						createdBy: user.id,
						isDeleted: false,
					},
					include: config.include,
				});

				// TODO: Handle relationship fields (tags, lists, linkedContacts, linkedCompanies, tasks)
				// These fields were extracted from the destructuring above to prevent Prisma errors
				// Future implementation should create ObjectTag, ObjectList, etc. relationships here

				// Create related records in parallel for better performance
				const createPromises = [];

				// Create PropertyLocation record (always create if we have any location data)
				if (address || location || website || reaData.neighborhood || neighborhood || subdivision || lotNumber || parcelNumber || zoning || reaData.zoning || reaData.lotNumber || reaData.parcelNumber) {
					createPromises.push(
						db.propertyLocation.create({
							data: {
								propertyId: result.id,
								address: address || undefined,
								location: location || undefined,
								website: website || undefined,
								neighborhood: reaApiData?.neighborhood || neighborhood || undefined,
								subdivision: subdivision || undefined,
								lotNumber: reaData.lotNumber || lotNumber || undefined,
								parcelNumber: reaData.parcelNumber || parcelNumber || undefined,
								zoning: reaData.zoning || zoning || undefined,
							},
						})
					);
				}

				// Create PropertyPhysicalDetails record (enhanced with REA data)
				if (reaData.yearBuilt || yearBuilt || reaData.buildingSquareFeet || squareFootage || units || floors || structures || 
					reaData.bedrooms || bedrooms || reaData.bathrooms || bathrooms || reaData.roomsCount || roomsCount || 
					reaData.buildingSquareFeet || buildingSquareFeet || reaData.garageSquareFeet || garageSquareFeet || 
					reaData.livingSquareFeet || livingSquareFeet || reaData.lotSquareFeet || lotSquareFeet || 
					reaData.lotSize || lotSize || lotType || reaData.lotAcres || lotAcres || 
					reaData.construction || construction || reaData.primaryUse || primaryUse || 
					reaData.propertyUse || propertyUse || reaData.class || propertyClass || parking || 
					reaData.parkingSpaces || parkingSpaces || garageType || heatingType || meterType || 
					reaData.legalDescription || legalDescription) {
					createPromises.push(
						db.propertyPhysicalDetails.create({
							data: {
								propertyId: result.id,
								yearBuilt: reaData.yearBuilt || yearBuilt || undefined,
								squareFootage: reaData.buildingSquareFeet || squareFootage || undefined,
								units: reaData.units || units || undefined,
								floors: reaData.floors || floors || undefined,
								structures: structures || undefined,
								bedrooms: reaData.bedrooms || bedrooms || undefined,
								bathrooms: reaData.bathrooms || bathrooms || undefined,
								roomsCount: reaData.roomsCount || roomsCount || undefined,
								buildingSquareFeet: reaData.buildingSquareFeet || buildingSquareFeet || undefined,
								garageSquareFeet: reaData.garageSquareFeet || garageSquareFeet || undefined,
								livingSquareFeet: reaData.livingSquareFeet || livingSquareFeet || undefined,
								lotSquareFeet: reaData.lotSquareFeet || lotSquareFeet || undefined,
								lotSize: reaData.lotSize || lotSize || undefined,
								lotType: lotType || undefined,
								lotAcres: reaData.lotAcres || lotAcres || undefined,
								construction: reaData.construction || construction || undefined,
								primaryUse: reaData.primaryUse || primaryUse || undefined,
								propertyUse: reaData.propertyUse || propertyUse || undefined,
								class: reaData.class || propertyClass || undefined,
								parking: parking || undefined,
								parkingSpaces: reaData.parkingSpaces || parkingSpaces || undefined,
								garageType: garageType || undefined,
								heatingType: heatingType || undefined,
								meterType: meterType || undefined,
								legalDescription: reaData.legalDescription || legalDescription || undefined,
							},
						})
					);
				}

				// Create PropertyFinancials record (enhanced with REA data)
				if (price !== undefined || reaData.estimatedValue || estimatedValue !== undefined || 
					reaData.pricePerSquareFoot || pricePerSquareFoot !== undefined || 
					equity !== undefined || equityPercent !== undefined || estimatedEquity !== undefined || 
					saleDate !== undefined || salePrice !== undefined || lastSalePrice !== undefined || 
					lastSaleDate !== undefined || reaData.landValue || landValue !== undefined || 
					reaData.buildingValue || buildingValue !== undefined || 
					cap !== undefined || exchange !== undefined || exchangeId !== undefined || 
					reaData.taxInfo || taxInfo !== undefined) {
					createPromises.push(
						db.propertyFinancials.create({
							data: {
								propertyId: result.id,
								price: price || undefined,
								estimatedValue: reaData.estimatedValue || estimatedValue || undefined,
								pricePerSquareFoot: reaData.pricePerSquareFoot || pricePerSquareFoot || undefined,
								equity: equity || undefined,
								equityPercent: equityPercent || undefined,
								estimatedEquity: estimatedEquity || undefined,
								saleDate: saleDate || undefined,
								salePrice: salePrice || undefined,
								lastSalePrice: lastSalePrice || undefined,
								lastSaleDate: lastSaleDate || undefined,
								landValue: reaData.landValue || landValue || undefined,
								buildingValue: reaData.buildingValue || buildingValue || undefined,
								cap: cap || undefined,
								exchange: exchange || undefined,
								exchangeId: exchangeId || undefined,
								taxInfo: reaData.taxInfo || taxInfo || undefined,
							},
						})
					);
				}

				// Create PropertyFlags record (always create this as it has default values)
				createPromises.push(
					db.propertyFlags.create({
						data: {
							propertyId: result.id,
							absenteeOwner: absenteeOwner || false,
							inStateAbsenteeOwner: inStateAbsenteeOwner || false,
							outOfStateAbsenteeOwner: outOfStateAbsenteeOwner || false,
							ownerOccupied: ownerOccupied || false,
							corporateOwned: corporateOwned || false,
							vacant: vacant || false,
							mobileHome: mobileHome || false,
							carport: reaData.carport || carport || false,
							auction: auction || false,
							cashBuyer: cashBuyer || false,
							investorBuyer: investorBuyer || false,
							freeClear: freeClear || false,
							highEquity: highEquity || false,
							privateLender: privateLender || false,
							deedInLieu: deedInLieu || false,
							quitClaim: quitClaim || false,
							sheriffsDeed: sheriffsDeed || false,
							warrantyDeed: warrantyDeed || false,
							inherited: inherited || false,
							spousalDeath: spousalDeath || false,
							lien: lien || false,
							taxLien: taxLien || false,
							preForeclosure: preForeclosure || false,
							trusteeSale: trusteeSale || false,
							floodZone: floodZone || false,
						},
					})
				);

				// Create PropertyMLS record if any MLS data is provided
				if (mlsActive !== undefined || mlsCancelled !== undefined || mlsFailed !== undefined || 
					mlsHasPhotos !== undefined || mlsPending !== undefined || mlsSold !== undefined || 
					mlsDaysOnMarket !== undefined || mlsListingPrice !== undefined || 
					mlsListingPricePerSquareFoot !== undefined || mlsSoldPrice !== undefined || 
					mlsStatus !== undefined || mlsType !== undefined || mlsListingDate !== undefined) {
					createPromises.push(
						db.propertyMLS.create({
							data: {
								propertyId: result.id,
								organizationId: validatedData.organizationId,
								mlsActive: mlsActive || false,
								mlsCancelled: mlsCancelled || false,
								mlsFailed: mlsFailed || false,
								mlsHasPhotos: mlsHasPhotos || false,
								mlsPending: mlsPending || false,
								mlsSold: mlsSold || false,
								mlsDaysOnMarket: mlsDaysOnMarket || undefined,
								mlsListingPrice: mlsListingPrice || undefined,
								mlsListingPricePerSquareFoot: mlsListingPricePerSquareFoot || undefined,
								mlsSoldPrice: mlsSoldPrice || undefined,
								mlsStatus: mlsStatus || undefined,
								mlsType: mlsType || undefined,
								mlsListingDate: mlsListingDate || undefined,
							},
						})
					);
				}

				// Create PropertyLegal record if any legal/environmental data is provided
				if (floodZoneDescription !== undefined || floodZoneType !== undefined || 
					noticeType !== undefined || reaId !== undefined || lastUpdateDate !== undefined) {
					createPromises.push(
						db.propertyLegal.create({
							data: {
								propertyId: result.id,
								floodZoneDescription: floodZoneDescription || undefined,
								floodZoneType: floodZoneType || undefined,
								noticeType: noticeType || undefined,
								reaId: reaId || undefined,
								lastUpdateDate: lastUpdateDate || undefined,
							},
						})
					);
				}

				// Create PropertyDemographics record (enhanced with REA data)
				if (reaData.fmrEfficiency || fmrEfficiency !== undefined || 
					reaData.fmrFourBedroom || fmrFourBedroom !== undefined || 
					reaData.fmrOneBedroom || fmrOneBedroom !== undefined || 
					reaData.fmrThreeBedroom || fmrThreeBedroom !== undefined || 
					reaData.fmrTwoBedroom || fmrTwoBedroom !== undefined || 
					reaData.fmrYear || fmrYear !== undefined || 
					reaData.hudAreaCode || hudAreaCode !== undefined || 
					reaData.hudAreaName || hudAreaName !== undefined || 
					reaData.medianIncome || medianIncome !== undefined || 
					reaData.suggestedRent || suggestedRent !== undefined) {
					createPromises.push(
						db.propertyDemographics.create({
							data: {
								propertyId: result.id,
								organizationId: validatedData.organizationId,
								fmrEfficiency: reaData.fmrEfficiency || fmrEfficiency || undefined,
								fmrFourBedroom: reaData.fmrFourBedroom || fmrFourBedroom || undefined,
								fmrOneBedroom: reaData.fmrOneBedroom || fmrOneBedroom || undefined,
								fmrThreeBedroom: reaData.fmrThreeBedroom || fmrThreeBedroom || undefined,
								fmrTwoBedroom: reaData.fmrTwoBedroom || fmrTwoBedroom || undefined,
								fmrYear: reaData.fmrYear || fmrYear || undefined,
								hudAreaCode: reaData.hudAreaCode || hudAreaCode || undefined,
								hudAreaName: reaData.hudAreaName || hudAreaName || undefined,
								medianIncome: reaData.medianIncome || medianIncome || undefined,
								suggestedRent: reaData.suggestedRent || suggestedRent || undefined,
							},
						})
					);
				}

				// Create PropertySaleHistory records from REA API data
				if (reaApiData?.saleHistory && Array.isArray(reaApiData.saleHistory)) {
					for (const sale of reaApiData.saleHistory) {
						if (sale.saleAmount || sale.saleDate) {
							createPromises.push(
								db.propertySaleHistory.create({
									data: {
										propertyId: result.id,
										organizationId: validatedData.organizationId,
										seller: sale.sellerNames || undefined,
										buyer: sale.buyerNames || undefined,
										saleDate: sale.saleDate || sale.recordingDate || undefined,
										salePrice: sale.saleAmount || undefined,
										askingPrice: sale.askingPrice || undefined,
										transactionType: sale.transactionType || undefined,
										pricePerSquareFoot: sale.pricePerSquareFoot || undefined,
										pricePerUnit: sale.pricePerUnit || undefined,
										transferredOwnershipPercentage: sale.transferredOwnershipPercentage || undefined,
										capRate: sale.capRate || undefined,
										grmRate: sale.grmRate || undefined,
									},
								})
							);
						}
					}
				}

				// Create PropertyMortgage records from REA API data
				if (reaApiData?.currentMortgages && Array.isArray(reaApiData.currentMortgages)) {
					for (const mortgage of reaApiData.currentMortgages) {
						createPromises.push(
							db.propertyMortgage.create({
								data: {
									propertyId: result.id,
									organizationId: validatedData.organizationId,
									amount: mortgage.amount || undefined,
									assumable: mortgage.assumable || undefined,
									deedType: mortgage.deedType ? { type: mortgage.deedType } : undefined,
									documentDate: mortgage.documentDate || undefined,
									documentNumber: mortgage.documentNumber ? { number: mortgage.documentNumber } : undefined,
									granteeName: mortgage.granteeName || undefined,
									interestRate: mortgage.interestRate || undefined,
									interestRateType: mortgage.interestRateType || undefined,
									lenderCode: mortgage.lenderCode || undefined,
									lenderName: mortgage.lenderName || undefined,
									lenderType: mortgage.lenderType || undefined,
									loanType: mortgage.loanType || undefined,
									loanTypeCode: mortgage.loanTypeCode || undefined,
									maturityDate: mortgage.maturityDate || undefined,
									mortgageId: mortgage.mortgageId || undefined,
									open: mortgage.open || undefined,
									position: mortgage.position || undefined,
									recordingDate: mortgage.recordingDate || undefined,
									seqNo: mortgage.seqNo || undefined,
									term: mortgage.term || undefined,
									termType: mortgage.termType || undefined,
									transactionType: mortgage.transactionType || undefined,
								},
							})
						);
					}
				}

				// Create PropertyMortgage records from mortgage history
				if (reaApiData?.mortgageHistory && Array.isArray(reaApiData.mortgageHistory)) {
					for (const mortgage of reaApiData.mortgageHistory) {
						// Skip if already added from currentMortgages
						const isDuplicate = reaApiData.currentMortgages?.some((current: any) => 
							current.mortgageId === mortgage.mortgageId && current.seqNo === mortgage.seqNo
						);
						if (!isDuplicate) {
							createPromises.push(
								db.propertyMortgage.create({
									data: {
										propertyId: result.id,
										organizationId: validatedData.organizationId,
										amount: mortgage.amount || undefined,
										assumable: mortgage.assumable || undefined,
										deedType: mortgage.deedType ? { type: mortgage.deedType } : undefined,
										documentDate: mortgage.documentDate || undefined,
										documentNumber: mortgage.documentNumber ? { number: mortgage.documentNumber } : undefined,
										granteeName: mortgage.granteeName || undefined,
										interestRate: mortgage.interestRate || undefined,
										interestRateType: mortgage.interestRateType || undefined,
										lenderCode: mortgage.lenderCode || undefined,
										lenderName: mortgage.lenderName || undefined,
										lenderType: mortgage.lenderType || undefined,
										loanType: mortgage.loanType || undefined,
										loanTypeCode: mortgage.loanTypeCode || undefined,
										maturityDate: mortgage.maturityDate || undefined,
										mortgageId: mortgage.mortgageId || undefined,
										open: mortgage.open !== undefined ? mortgage.open : false,
										position: mortgage.position || undefined,
										recordingDate: mortgage.recordingDate || undefined,
										seqNo: mortgage.seqNo || undefined,
										term: mortgage.term || undefined,
										termType: mortgage.termType || undefined,
										transactionType: mortgage.transactionType || undefined,
									},
								})
							);
						}
					}
				}

				// Create PropertyForeclosureInfo records from REA API data
				if (reaApiData?.foreclosureInfo && Array.isArray(reaApiData.foreclosureInfo)) {
					for (const foreclosure of reaApiData.foreclosureInfo) {
						createPromises.push(
							db.propertyForeclosureInfo.create({
								data: {
									propertyId: result.id,
									organizationId: validatedData.organizationId,
									foreclosureId: foreclosure.foreclosureId ? { id: foreclosure.foreclosureId } : undefined,
									originalLoanAmount: foreclosure.originalLoanAmount || undefined,
									estimatedBankValue: foreclosure.estimatedBankValue || undefined,
									defaultAmount: foreclosure.defaultAmount || undefined,
									recordingDate: foreclosure.recordingDate || undefined,
									openingBid: foreclosure.openingBid || undefined,
									auctionDate: foreclosure.auctionDate || undefined,
									auctionTime: foreclosure.auctionTime || undefined,
									auctionStreetAddress: foreclosure.auctionStreetAddress || undefined,
									documentType: foreclosure.documentType || undefined,
									trusteeSaleNumber: foreclosure.trusteeSaleNumber ? { number: foreclosure.trusteeSaleNumber } : undefined,
									typeName: foreclosure.typeName || undefined,
									active: foreclosure.active || undefined,
									lenderName: foreclosure.lenderName || undefined,
									lenderPhone: foreclosure.lenderPhone || undefined,
									noticeType: foreclosure.noticeType || undefined,
									seqNo: foreclosure.seqNo || undefined,
									trusteeAddress: foreclosure.trusteeAddress || undefined,
									trusteeName: foreclosure.trusteeName || undefined,
									trusteePhone: foreclosure.trusteePhone || undefined,
									judgmentDate: foreclosure.judgmentDate || undefined,
									judgmentAmount: foreclosure.judgmentAmount || undefined,
								},
							})
						);
					}
				}

				// Create PropertyMlsHistory records from REA API data
				if (reaApiData?.mlsHistory && Array.isArray(reaApiData.mlsHistory)) {
					for (const mls of reaApiData.mlsHistory) {
						createPromises.push(
							db.propertyMlsHistory.create({
								data: {
									propertyId: result.id,
									organizationId: validatedData.organizationId,
									mlsId: mls.mlsId || undefined,
									type: mls.type || undefined,
									price: mls.price || undefined,
									beds: mls.beds || undefined,
									baths: mls.baths || undefined,
									daysOnMarket: mls.daysOnMarket || undefined,
									agentName: mls.agentName || undefined,
									agentOffice: mls.agentOffice || undefined,
									agentPhone: mls.agentPhone || undefined,
									agentEmail: mls.agentEmail || undefined,
									status: mls.status || undefined,
									statusDate: mls.statusDate || undefined,
									lastStatusDate: mls.lastStatusDate || undefined,
								},
							})
						);
					}
				}

				// Execute all creation promises in parallel
				await Promise.all(createPromises);

				// Fetch the complete property with all related data for response
				result = await db.property.findUnique({
					where: { id: result.id },
					include: config.include,
				});
			} else {
				// Standard creation for other object types
				const table = getDbTable(objectType);
				result = await table.create({
					data: {
						...validatedData,
						createdBy: user.id,
						isDeleted: false,
					},
					include: config.include,
				});
			}

			// Create initial status history for status fields
			STATUS_FIELDS.forEach((field) => {
				const statusValue = (result as any)[field];
				if (statusValue) {
					createStatusHistory(
						result.id,
						objectType,
						field,
						null, // fromStatus is null for creation
						statusValue,
						user.id,
						validatedData.organizationId,
					);
				}
			});

			// Create system activity for record creation
			const recordName = (result as any).name || 
				(result as any).firstName && (result as any).lastName 
					? `${(result as any).firstName} ${(result as any).lastName}`
					: undefined;
			
			createRecordCreatedActivity(
				result.id,
				objectType,
				validatedData.organizationId,
				user.id,
				recordName
			);

			const transformedResult = config.transformResult(result);

			logger.info(`${objectType} created: ${result.id}`);
			return c.json(
				{ [objectType]: transformedResult },
				201,
			);
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(
				`Failed to create ${objectTypeName.slice(0, -1)}:`,
				error,
			);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Get objects with filters and search
objectsRouter.get(
	"/objects/:objectType",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const objectType = c.get("objectType" as any) as ObjectType;

			const organizationId = c.req.query("organizationId");
			const limit = Number.parseInt(c.req.query("limit") || "50");
			const offset = Number.parseInt(c.req.query("offset") || "0");
			const search = c.req.query("search");

			if (!organizationId) {
				return c.json(
					{
						error: "Organization ID is required fetching objects with filters",
					},
					400,
				);
			}

			await verifyOrganizationMembership(
				organizationId,
				c.get("user").id,
			);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Build dynamic filters based on query parameters
			const additionalFilters: any = { organizationId };

			// Add object-specific filters
			Object.keys(config.filterFields).forEach((field) => {
				const value = c.req.query(field);
				if (value) {
					const filterType = (config.filterFields as any)[field];

					if (filterType === "string") {
						additionalFilters[field] = {
							contains: value,
							mode: "insensitive",
						};
					} else if (
						filterType === "dateRange" &&
						field === "createdAt"
					) {
						// Handle date range filtering
						try {
							if (value.includes(":")) {
								const [startTimestamp, endTimestamp] =
									value.split(":");
								const startDate = new Date(
									Number.parseInt(startTimestamp),
								);
								const endDate = new Date(
									Number.parseInt(endTimestamp),
								);

								if (
									!isNaN(startDate.getTime()) &&
									!isNaN(endDate.getTime())
								) {
									additionalFilters.createdAt = {
										gte: startDate,
										lte: endDate,
									};
								}
							} else {
								const timestamp = Number.parseInt(value);
								const date = new Date(timestamp);

								if (!isNaN(date.getTime())) {
									const startOfDay = new Date(date);
									startOfDay.setUTCHours(0, 0, 0, 0);
									const endOfDay = new Date(date);
									endOfDay.setUTCHours(23, 59, 59, 999);

									additionalFilters.createdAt = {
										gte: startOfDay,
										lte: endOfDay,
									};
								}
							}
						} catch (error) {
							logger.error(
								"Error parsing createdAt filter:",
								error,
							);
						}
					} else {
						additionalFilters[field] = value;
					}
				}
			});

			// Handle tags filtering
			const tags = c.req.query("tags");
			if (tags) {
				const tagNames = tags.split(",").map(tag => tag.trim()).filter(Boolean);
				if (tagNames.length > 0) {
					// Import normalization function
					const { normalizeTagName } = await import("../../lib/tag-utils");
					
					// Normalize tag names for consistent matching
					const normalizedTagNames = tagNames.map(tagName => normalizeTagName(tagName));
					
					additionalFilters.objectTags = {
						some: {
							tag: {
								name: {
									in: normalizedTagNames,
								},
								organizationId,
							},
						},
					};
				}
			}

			const whereConditions = buildSearchConditions(
				objectType,
				search || "",
				additionalFilters,
			);

					// Use defensive approach for user relations to handle missing users
		const includeConfig = { ...config.include };
		// Remove user relations if they exist to handle separately
		if ('creator' in includeConfig) delete (includeConfig as any).creator;
		if ('updater' in includeConfig) delete (includeConfig as any).updater;

		const [results, total] = await Promise.all([
				table.findMany({
					where: whereConditions,
					include: includeConfig,
					orderBy: { createdAt: "desc" },
					take: limit,
					skip: offset,
					cacheStrategy: {
						ttl: 300, // 5 minutes cache
						tags: [`objects_${objectType}_org_${organizationId}`, `org_${organizationId}`],
					},
				}),
				table.count({ 
					where: whereConditions,
					cacheStrategy: {
						ttl: 300, // 5 minutes cache  
						tags: [`objects_count_${objectType}_org_${organizationId}`, `org_${organizationId}`],
					},
				}),
			]);

			// For list queries, we'll use minimal user info or skip user relations for performance
			// The transform function will handle missing creator by showing "System"
			const resultsWithMinimalUsers = results.map(result => ({
				...result,
				creator: null, // Will be handled by transform function to show "System"
				updater: null,
			}));

			const transformedResults = resultsWithMinimalUsers.map(config.transformResult);

			return c.json({
				[objectType]: transformedResults,
				pagination: {
					total,
					limit,
					offset,
					hasMore: offset + limit < total,
				},
			});
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(`Failed to fetch ${objectTypeName}:`, error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Get single object
objectsRouter.get(
	"/objects/:objectType/:id",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const objectType = c.get("objectType" as any) as ObjectType;
			const id = c.req.param("id");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json(
					{
						error: "Organization ID is required fetching single object",
					},
					400,
				);
			}

			await verifyOrganizationMembership(
				organizationId,
				c.get("user").id,
			);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

					// Use defensive approach for user relations to handle missing users
		const includeConfigSingle = { ...config.include };
		// Remove user relations if they exist to handle separately
		if ('creator' in includeConfigSingle) delete (includeConfigSingle as any).creator;
		if ('updater' in includeConfigSingle) delete (includeConfigSingle as any).updater;

				const result = await table.findFirst({
			where: {
				id,
				organizationId,
				isDeleted: false,
			},
			include: includeConfigSingle,
				cacheStrategy: {
					ttl: 600, // 10 minutes cache for individual objects
					tags: [`object_${objectType}_${id}`, `org_${organizationId}`],
				},
			});

			if (!result) {
				return c.json(
					{ error: `${objectType} not found` },
					404,
				);
			}

					// Separately fetch user relations to handle missing users gracefully
		const [creatorResult, updaterResult] = await Promise.allSettled([
			result.createdBy ? db.user.findUnique({
				where: { id: result.createdBy },
				select: { id: true, name: true, email: true, image: true },
			}) : null,
			result.updatedBy ? db.user.findUnique({
				where: { id: result.updatedBy },
				select: { id: true, name: true, email: true, image: true },
			}) : null,
		]);

		// Attach user relations to the result
		const resultWithUsers = {
			...result,
			creator: creatorResult.status === 'fulfilled' ? creatorResult.value : null,
			updater: updaterResult.status === 'fulfilled' ? updaterResult.value : null,
		};

			const transformedResult = config.transformResult(resultWithUsers);

			return c.json({ [objectType]: transformedResult });
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(
				`Failed to fetch ${objectTypeName.slice(0, -1)}:`,
				error,
			);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Update object
objectsRouter.patch(
	"/objects/:objectType/:id",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;
			const id = c.req.param("id");
			const body = await c.req.json();

			const config = OBJECT_CONFIG[objectType];
			const validatedData = config.updateSchema.parse(body);

			const organizationId =
				c.req.query("organizationId") || body.organizationId;

			if (!organizationId) {
				return c.json(
					{ error: "Organization ID is required updating object" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const table = getDbTable(objectType);

			// Get the existing record to track changes
			const existingRecord = await table.findFirst({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
			});

			if (!existingRecord) {
				return c.json(
					{ error: `${objectType} not found` },
					404,
				);
			}

			const result = await table.update({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
				data: {
					...validatedData,
					updatedBy: user.id,
				},
				include: config.include,
			});

			// Detect and create system activities for field changes
			const changes = detectFieldChanges(existingRecord, validatedData);
			if (changes.length > 0) {
				createFieldChangeActivity(
					result.id,
					objectType,
					organizationId,
					user.id,
					changes
				);
			}

			const transformedResult = config.transformResult(result);

			logger.info(`${objectType} updated: ${result.id}`);
			return c.json({ [objectType]: transformedResult });
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(
				`Failed to update ${objectTypeName.slice(0, -1)}:`,
				error,
			);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Update specific field
objectsRouter.patch(
	"/objects/:objectType/:id/field",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;
			const id = c.req.param("id");
			const { field, value, organizationId } = await c.req.json();

			if (!field || !organizationId) {
				return c.json(
					{ error: "Field and organizationId are required" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Verify object exists and user has access
			const existing = await table.findFirst({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
				cacheStrategy: {
					ttl: 300, // 5 minutes cache for verification checks
					tags: [`object_${objectType}_${id}`, `org_${organizationId}`],
				},
			});

			if (!existing) {
				return c.json(
					{ error: `${objectType} not found` },
					404,
				);
			}

			const updateData: any = {
				[field]: value,
				updatedBy: user.id,
			};

			const result = await table.update({
				where: { id },
				data: updateData,
				include: config.include,
			});

			// Create status history if this is a status field
			if (isStatusField(field) && value !== (existing as any)[field]) {
				await createStatusHistory(
					id,
					objectType,
					field,
					(existing as any)[field] || null,
					value,
					user.id,
					organizationId,
				);
			}

			// Create system activity for field change
			if (value !== (existing as any)[field]) {
				const changes = [{
					field,
					oldValue: (existing as any)[field],
					newValue: value
				}];
				
				createFieldChangeActivity(
					id,
					objectType,
					organizationId,
					user.id,
					changes
				);
			}

			// Ensure result exists before transforming
			if (!result || !result.id) {
				logger.error(
					`Database update succeeded but returned invalid result for ${objectType} ${id}:`,
					result,
				);
				return c.json({ error: "Update failed - invalid result from database" }, 500);
			}

			const transformedResult = config.transformResult(result);

			// Ensure transform was successful
			if (!transformedResult || !transformedResult.id) {
				logger.error(
					`Transform failed for ${objectType} ${id}:`,
					{ result, transformedResult },
				);
				return c.json({ error: "Update failed - transform error" }, 500);
			}

			logger.info(
				`${objectType} field updated: ${result.id}, field: ${field}`,
			);
			return c.json({ [objectType]: transformedResult });
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(
				`Failed to update ${objectTypeName.slice(0, -1)} field:`,
				error,
			);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Clear specific field
objectsRouter.delete(
	"/objects/:objectType/:id/field/:field",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;
			const id = c.req.param("id");
			const field = c.req.param("field");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json(
					{ error: "Organization ID is required clearing field" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Verify object exists and user has access
			const existing = await table.findFirst({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
				cacheStrategy: {
					ttl: 300, // 5 minutes cache for verification checks
					tags: [`object_${objectType}_${id}`, `org_${organizationId}`],
				},
			});

			if (!existing) {
				return c.json(
					{ error: `${objectType} not found` },
					404,
				);
			}

			const updateData: any = {
				[field]:
					field === "email" ||
					field === "phone" ||
					field === "address"
						? []
						: null,
				updatedBy: user.id,
			};

			const result = await table.update({
				where: { id },
				data: updateData,
				include: config.include,
			});

			// Create system activity for field clearing
			const oldValue = (existing as any)[field];
			const newValue = field === "email" || field === "phone" || field === "address" ? [] : null;
			
			if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
				const changes = [{
					field,
					oldValue,
					newValue
				}];
				
				createFieldChangeActivity(
					id,
					objectType,
					organizationId,
					user.id,
					changes
				);
			}

			// Ensure result exists before transforming
			if (!result || !result.id) {
				logger.error(
					`Database update succeeded but returned invalid result for ${objectType} ${id}:`,
					result,
				);
				return c.json({ error: "Clear failed - invalid result from database" }, 500);
			}

			const transformedResult = config.transformResult(result);

			// Ensure transform was successful
			if (!transformedResult || !transformedResult.id) {
				logger.error(
					`Transform failed for ${objectType} ${id}:`,
					{ result, transformedResult },
				);
				return c.json({ error: "Clear failed - transform error" }, 500);
			}

			logger.info(
				`${objectType} field cleared: ${result.id}, field: ${field}`,
			);
			return c.json({ [objectType]: transformedResult });
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(
				`Failed to clear ${objectTypeName.slice(0, -1)} field:`,
				error,
			);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Batch delete objects (MUST come before single delete to avoid route conflicts)
objectsRouter.delete(
	"/objects/:objectType/delete-batch",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;
			const body = await c.req.json();

			const { ids, organizationId } = body;

			if (!ids || !Array.isArray(ids) || ids.length === 0) {
				return c.json({ error: "Array of IDs is required" }, 400);
			}

			if (!organizationId) {
				return c.json(
					{ error: "Organization ID is required for batch delete" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const table = getDbTable(objectType);

			// Perform batch deletion in a transaction
			const result = await db.$transaction(async (tx) => {
				// First, delete any favorites that reference these objects
				const deletedFavorites = await tx.favorite.deleteMany({
					where: {
						objectId: { in: ids },
						objectType: objectType,
						organizationId,
					},
				});

				// Then, soft delete all the objects
				const deletedObjects = await (tx as any)[
					OBJECT_CONFIG[objectType].tableName
				].updateMany({
					where: {
						id: { in: ids },
						organizationId,
						isDeleted: false,
					},
					data: {
						isDeleted: true,
						deletedAt: new Date(),
						deletedBy: user.id,
					},
				});

				return { deletedObjects, deletedFavorites };
			});

			// Create system activities for batch deletion
			// Note: This is best effort - we don't have individual record details here
			createSystemActivity({
				recordId: "batch", // Special identifier for batch operations
				recordType: objectType,
				organizationId,
				userId: user.id,
				message: `${result.deletedObjects.count} ${objectType} were deleted`,
				type: "system"
			});

			logger.info(
				`Batch deleted ${result.deletedObjects.count} ${objectType}, favorites cascade deleted: ${result.deletedFavorites.count}`,
			);

			return c.json({
				message: `${result.deletedObjects.count} ${objectType} deleted successfully`,
				deletedCount: result.deletedObjects.count,
			});
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(`Failed to batch delete ${objectTypeName}:`, error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Soft delete single object (MUST come after batch delete to avoid route conflicts)
objectsRouter.delete(
	"/objects/:objectType/:id",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;
			const id = c.req.param("id");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json(
					{ error: "Organization ID is required deleting object" },
					400,
				);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const table = getDbTable(objectType);

			// Get record details before deletion for activity tracking
			const recordToDelete = await table.findFirst({
				where: {
					id,
					organizationId,
					isDeleted: false,
				},
			});

			// Perform both operations in a transaction
			await db.$transaction(async (tx) => {
				// First, delete any favorites that reference this object
				const deletedFavorites = await tx.favorite.deleteMany({
					where: {
						objectId: id,
						objectType: objectType,
						organizationId,
					},
				});

				// Then, soft delete the object
				await (tx as any)[OBJECT_CONFIG[objectType].tableName].update({
					where: {
						id,
						organizationId,
						isDeleted: false,
					},
					data: {
						isDeleted: true,
						deletedAt: new Date(),
						deletedBy: user.id,
					},
				});

				logger.info(
					`${objectType} deleted: ${id}, favorites cascade deleted: ${deletedFavorites.count}`,
				);
			});

			// Create system activity for deletion
			if (recordToDelete) {
				const recordName = (recordToDelete as any).name || 
					(recordToDelete as any).firstName && (recordToDelete as any).lastName 
						? `${(recordToDelete as any).firstName} ${(recordToDelete as any).lastName}`
						: undefined;
				
				createRecordDeletedActivity(
					id,
					objectType,
					organizationId,
					user.id,
					recordName
				);
			}

			return c.json({
				message: `${objectType} deleted successfully`,
			});
		} catch (error) {
			const objectTypeName = c.get("objectType" as any) as ObjectType;
			logger.error(
				`Failed to delete ${objectTypeName.slice(0, -1)}:`,
				error,
			);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Get status history for an object
objectsRouter.get(
	"/objects/:objectType/:id/status-history",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const objectType = c.get("objectType" as any) as ObjectType;
			const id = c.req.param("id");
			const organizationId = c.req.query("organizationId");

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			await verifyOrganizationMembership(
				organizationId,
				c.get("user").id,
			);

			const statusHistory = await db.objectStatusHistory.findMany({
				where: {
					objectId: id,
					objectType,
					organizationId,
				},
				include: {
					user: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
				},
				orderBy: {
					createdAt: "desc",
				},
				cacheStrategy: {
					ttl: 600, // 10 minutes cache for status history
					tags: [`status_history_${objectType}_${id}`, `org_${organizationId}`],
				},
			});

			return c.json({ statusHistory });
		} catch (error) {
			logger.error("Failed to fetch status history:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Add the navigation routes - mount at root level
objectsRouter.route("/navigation", recordNavigationRouter);

export type ObjectsRouter = typeof objectsRouter;
