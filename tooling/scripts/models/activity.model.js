import mongoose from "mongoose";

const ActivitySchema = mongoose.Schema(
  {
    description: String,
    subject: String,
    email: {
      type: String,
    },
    cc: {
      type: String,
    },
    bcc: {
      type: String,
    },
    title: {
      type: String,
    },
    noteType: {
      type: String,
    },
    selectedPhone: {
      type: String,
    },
    callResult: {
      type: String,
    },
    team: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Team",
    },
    contactId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ContactDetails",
    },
    propertyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PropertyDetails",
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  { timestamps: true }
);

export default mongoose.model("Activity", ActivitySchema);
