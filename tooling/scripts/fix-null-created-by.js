// MongoDB script to fix null createdBy fields
// This script will update all null createdBy values to a valid user ObjectId

print('🔍 Starting null createdBy fix process...');

// First, let's find a valid user to use as fallback
print('📋 Finding a valid user for fallback...');
const validUser = db.user.findOne({}, { _id: 1, name: 1, email: 1 });

if (!validUser) {
    print('❌ No users found in database! Creating a system user...');
    
    // Create a system user as fallback
    const systemUserId = ObjectId('000000000000000000000000');
    const systemUser = {
        _id: systemUserId,
        name: 'System User',
        email: '<EMAIL>',
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
    };
    
    try {
        db.user.insertOne(systemUser);
        print('✅ Created system user: ' + systemUserId);
        fallbackUserId = systemUserId;
    } catch (error) {
        print('❌ Failed to create system user: ' + error);
        quit(1);
    }
} else {
    fallbackUserId = validUser._id;
    print('✅ Using existing user as fallback:');
    print('  - ID: ' + validUser._id);
    print('  - Name: ' + (validUser.name || 'N/A'));
    print('  - Email: ' + (validUser.email || 'N/A'));
}

// Define collections that have createdBy fields
const collectionsToFix = [
    'contact',
    'company', 
    'property',
    'related_contact',
    'linked_contact',
    'custom_object',
    'list',
    'object_relationship',
    'tag',
    'object_view',
    'pin'
];

// Function to update null createdBy values in a collection
function fixNullCreatedBy(collectionName) {
    print(`\n🔧 Fixing collection: ${collectionName}`);
    
    try {
        const collection = db.getCollection(collectionName);
        
        // Check if collection exists
        if (!collection.findOne()) {
            print(`  ⏭️ Collection is empty or doesn't exist, skipping...`);
            return { matchedCount: 0, modifiedCount: 0 };
        }
        
        // Count documents with null createdBy
        const nullCount = collection.countDocuments({ createdBy: null });
        print(`  📊 Found ${nullCount} documents with null createdBy`);
        
        if (nullCount === 0) {
            print(`  ✅ No null createdBy values found`);
            return { matchedCount: 0, modifiedCount: 0 };
        }
        
        // Update null createdBy values
        const result = collection.updateMany(
            { createdBy: null },
            { $set: { createdBy: fallbackUserId } }
        );
        
        print(`  ✅ Updated ${result.modifiedCount} documents`);
        print(`  📈 Matched: ${result.matchedCount}, Modified: ${result.modifiedCount}, Acknowledged: ${result.acknowledged}`);
        
        return result;
        
    } catch (error) {
        print(`  ❌ Error updating ${collectionName}: ${error}`);
        return { matchedCount: 0, modifiedCount: 0, error: error };
    }
}

// Process all collections
let totalMatched = 0;
let totalModified = 0;
let results = {};

print('\n🚀 Processing collections...');
collectionsToFix.forEach(collectionName => {
    const result = fixNullCreatedBy(collectionName);
    results[collectionName] = result;
    totalMatched += result.matchedCount || 0;
    totalModified += result.modifiedCount || 0;
});

// Summary
print('\n📊 SUMMARY');
print('='.repeat(50));
print(`Total documents matched: ${totalMatched}`);
print(`Total documents modified: ${totalModified}`);
print(`Collections processed: ${collectionsToFix.length}`);

// Detailed results
print('\n📋 DETAILED RESULTS');
print('='.repeat(50));
Object.keys(results).forEach(collectionName => {
    const result = results[collectionName];
    if (result.matchedCount > 0 || result.error) {
        print(`${collectionName}:`);
        print(`  - Matched: ${result.matchedCount || 0}`);
        print(`  - Modified: ${result.modifiedCount || 0}`);
        if (result.error) {
            print(`  - Error: ${result.error}`);
        }
    }
});

// Verification step
print('\n🔍 VERIFICATION');
print('='.repeat(50));
collectionsToFix.forEach(collectionName => {
    try {
        const collection = db.getCollection(collectionName);
        const nullCount = collection.countDocuments({ createdBy: null });
        const validCount = collection.countDocuments({ createdBy: fallbackUserId });
        
        if (nullCount > 0 || validCount > 0) {
            print(`${collectionName}:`);
            print(`  - Remaining null createdBy: ${nullCount}`);
            print(`  - Using fallback user: ${validCount}`);
        }
    } catch (error) {
        print(`Error verifying ${collectionName}: ${error}`);
    }
});

// Test a specific contact query to see if the issue is resolved
print('\n🧪 TESTING CONTACT QUERY');
print('='.repeat(50));
try {
    const testContact = db.contact.findOne({}, { _id: 1, firstName: 1, lastName: 1, createdBy: 1 });
    if (testContact) {
        print('✅ Successfully queried a contact:');
        print(`  - ID: ${testContact._id}`);
        print(`  - Name: ${testContact.firstName || ''} ${testContact.lastName || ''}`);
        print(`  - CreatedBy: ${testContact.createdBy}`);
        
        // Check if the createdBy user exists
        const creator = db.user.findOne({ _id: testContact.createdBy }, { name: 1, email: 1 });
        if (creator) {
            print(`  - Creator exists: ${creator.name} (${creator.email})`);
        } else {
            print(`  - ⚠️ Creator user not found in database`);
        }
    } else {
        print('⚠️ No contacts found for testing');
    }
} catch (error) {
    print('❌ Test contact query failed: ' + error);
}

print('\n🎉 Null createdBy fix process completed!');
print('You can now test your application to see if the Prisma relation error is resolved.'); 