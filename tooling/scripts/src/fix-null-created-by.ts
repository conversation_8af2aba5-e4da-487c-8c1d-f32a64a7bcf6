import { db } from '@repo/database/server';

async function fixNullCreatedByValues() {
  try {
    console.log('🔍 Starting null createdBy fix process...');
    
    // Get a fallback user - we'll use the first user we can find
    let fallbackUser = await db.user.findFirst({
      select: { id: true, name: true, email: true }
    });
    
    if (!fallbackUser) {
      console.log('⚠️ No users found in database. This might be the core issue.');
      console.log('🔧 Creating a system user as fallback...');
      try {
        fallbackUser = await db.user.create({
          data: {
            id: '000000000000000000000000',
            name: 'System User',
            email: '<EMAIL>',
            emailVerified: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
        console.log(`✅ Created system user: ${fallbackUser.id}`);
      } catch (error) {
        console.error('❌ Failed to create system user:', error);
        return;
      }
    }
    
    console.log(`Using fallback user: ${fallbackUser.name} (${fallbackUser.email}) - ID: ${fallbackUser.id}`);
    
    // Try to query contacts and see what happens
    console.log('🔍 Attempting to query contacts to identify the issue...');
    
    let contactsWithIssues = 0;
    let totalContacts = 0;
    let fixedContacts = 0;
    
    try {
      // Get contacts in small batches and try to identify problematic ones
      const batchSize = 100;
      let skip = 0;
      let hasMore = true;
      
      while (hasMore) {
        try {
          const contacts = await db.contact.findMany({
            select: { id: true, firstName: true, lastName: true, createdBy: true },
            skip,
            take: batchSize
          });
          
          if (contacts.length === 0) {
            hasMore = false;
            break;
          }
          
          totalContacts += contacts.length;
          
          for (const contact of contacts) {
            // Check if createdBy is missing or invalid
            if (!contact.createdBy) {
              contactsWithIssues++;
              
              try {
                // Try to update this contact
                await db.contact.update({
                  where: { id: contact.id },
                  data: { createdBy: fallbackUser.id }
                });
                fixedContacts++;
                console.log(`✅ Fixed contact ${contact.id} (${contact.firstName} ${contact.lastName})`);
              } catch (updateError) {
                console.log(`❌ Failed to fix contact ${contact.id}:`, updateError);
              }
            }
          }
          
          skip += batchSize;
          
          // Log progress every 500 contacts
          if (totalContacts % 500 === 0) {
            console.log(`📊 Progress: Processed ${totalContacts} contacts, found ${contactsWithIssues} with issues, fixed ${fixedContacts}`);
          }
          
        } catch (batchError) {
          console.log(`⚠️ Error processing batch starting at ${skip}:`, batchError);
          skip += batchSize;
        }
      }
      
      console.log(`📊 Final stats: Processed ${totalContacts} contacts, found ${contactsWithIssues} with null createdBy, fixed ${fixedContacts}`);
      
    } catch (error) {
      console.log('❌ Error during contact processing:', error);
    }
    
    // Now try to test if we can query a contact with relations
    console.log('🔍 Testing contact query with creator relation...');
    try {
      const testContact = await db.contact.findFirst({
        include: {
          creator: {
            select: { id: true, name: true, email: true }
          }
        }
      });
      
      if (testContact) {
        console.log(`✅ Test successful! Contact ${testContact.id} has creator: ${testContact.creator.name}`);
        console.log('🎉 The fix appears to have worked!');
      } else {
        console.log('⚠️ No contacts found for testing');
      }
    } catch (error) {
      console.log('❌ Test query still failing:', error);
      console.log('The issue might be more complex than just null createdBy values.');
    }
    
  } catch (error) {
    console.error('❌ Error in fix process:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  fixNullCreatedByValues().catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export default fixNullCreatedByValues; 