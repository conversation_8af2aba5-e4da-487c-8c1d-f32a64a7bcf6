import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

interface OldActivity {
  _id: { $oid: string };
  description?: string;
  subject?: string;
  email?: string;
  cc?: string;
  bcc?: string;
  title?: string;
  noteType?: string; // "Call", "Note", "Email", "Meeting", etc.
  selectedPhone?: string;
  callResult?: string;
  team: { $oid: string };
  contactId?: { $oid: string };
  propertyId?: { $oid: string }; // Added property ID support
  userId: { $oid: string };
  createdAt: { $date: string };
  updatedAt: { $date: string };
  __v?: number;
}

function mapActivityType(oldNoteType: string): string {
  const typeMap: { [key: string]: string } = {
    'Call': 'call',
    'Note': 'note', 
    'Email': 'email',
    'Meeting': 'meeting',
    'Task': 'task',
    'System': 'system'
  };
  
  return typeMap[oldNoteType] || 'note'; // Default to 'note' if type not recognized
}

function buildActivityMessage(activity: OldActivity): string {
  const parts: string[] = [];
  
  // Start with main description
  if (activity.description) {
    parts.push(activity.description);
  }
  
  // Add title if different from description
  if (activity.title && activity.title !== activity.description) {
    if (parts.length > 0) {
      parts.push(`\n\nTitle: ${activity.title}`);
    } else {
      parts.push(activity.title);
    }
  }
  
  // Add subject if different from description and title
  if (activity.subject && 
      activity.subject !== activity.description && 
      activity.subject !== activity.title) {
    parts.push(`\nSubject: ${activity.subject}`);
  }
  
  // Add email metadata for email activities
  if (activity.email || activity.cc || activity.bcc) {
    const emailMeta: string[] = [];
    if (activity.email) emailMeta.push(`To: ${activity.email}`);
    if (activity.cc) emailMeta.push(`CC: ${activity.cc}`);
    if (activity.bcc) emailMeta.push(`BCC: ${activity.bcc}`);
    
    if (emailMeta.length > 0) {
      parts.push(`\n\nEmail Details:\n${emailMeta.join('\n')}`);
    }
  }
  
  return parts.join('') || 'No description provided';
}

function parseDate(dateObj: { $date: string }): Date {
  return new Date(dateObj.$date);
}

// Cache for organizations, users, contacts, and properties to reduce database queries
const organizationCache = new Map<string, boolean>();
const userCache = new Map<string, boolean>();
const contactCache = new Map<string, boolean>();
const propertyCache = new Map<string, boolean>();

async function validateReferences(activity: OldActivity): Promise<{ 
  validOrg: boolean; 
  validUser: boolean; 
  validContact?: boolean;
  validProperty?: boolean;
}> {
  // Check organization cache first
  let validOrg = organizationCache.get(activity.team.$oid);
  if (validOrg === undefined) {
    const org = await prisma.organization.findUnique({
      where: { id: activity.team.$oid }
    });
    validOrg = !!org;
    organizationCache.set(activity.team.$oid, validOrg);
  }
  
  // Check user cache first
  let validUser = userCache.get(activity.userId.$oid);
  if (validUser === undefined) {
    const user = await prisma.user.findUnique({
      where: { id: activity.userId.$oid }
    });
    validUser = !!user;
    userCache.set(activity.userId.$oid, validUser);
  }
  
  // Check contact if provided
  let validContact: boolean | undefined;
  if (activity.contactId) {
    validContact = contactCache.get(activity.contactId.$oid);
    if (validContact === undefined) {
      const contact = await prisma.contact.findUnique({
        where: { id: activity.contactId.$oid }
      });
      validContact = !!contact;
      contactCache.set(activity.contactId.$oid, validContact);
    }
  }
  
  // Check property if provided
  let validProperty: boolean | undefined;
  if (activity.propertyId) {
    validProperty = propertyCache.get(activity.propertyId.$oid);
    if (validProperty === undefined) {
      const property = await prisma.property.findUnique({
        where: { id: activity.propertyId.$oid }
      });
      validProperty = !!property;
      propertyCache.set(activity.propertyId.$oid, validProperty);
    }
  }
  
  return { validOrg, validUser, validContact, validProperty };
}

async function migrateActivityBatch(activities: OldActivity[]): Promise<{ success: number; skipped: number; failed: number }> {
  let success = 0;
  let skipped = 0;
  let failed = 0;
  
  for (const activity of activities) {
    try {
      // Skip if activity already exists
      const existingActivity = await prisma.activity.findUnique({
        where: { id: activity._id.$oid }
      });
      
      if (existingActivity) {
        skipped++;
        continue;
      }
      
      // Validate references
      const { validOrg, validUser, validContact, validProperty } = await validateReferences(activity);
      
      if (!validOrg) {
        console.log(`⚠️ Skipping activity ${activity._id.$oid}: Organization ${activity.team.$oid} not found`);
        skipped++;
        continue;
      }
      
      if (!validUser) {
        console.log(`⚠️ Skipping activity ${activity._id.$oid}: User ${activity.userId.$oid} not found`);
        skipped++;
        continue;
      }
      
      // Determine record relationship priority: contact > property > none
      let recordId = null;
      let recordType = null;
      
      if (activity.contactId && validContact) {
        recordId = activity.contactId.$oid;
        recordType = 'contact';
      } else if (activity.propertyId && validProperty) {
        recordId = activity.propertyId.$oid;
        recordType = 'property';
      }
      // If neither contact nor property exists, we still create the activity without the record link
      
      // Build comprehensive message
      const message = buildActivityMessage(activity);
      
      // Create the activity with all preserved fields
      await prisma.activity.create({
        data: {
          id: activity._id.$oid, // Preserve original ID
          organizationId: activity.team.$oid,
          userId: activity.userId.$oid,
          recordId,
          recordType,
          type: mapActivityType(activity.noteType || 'Note') as any,
          message,
          
          // Call-specific fields (preserve phone and result)
          phone: activity.selectedPhone || null,
          result: activity.callResult || null,
          
          // Default values for new fields
          resolved: false,
          system: false,
          edited: false,
          mentionedUsers: [],
          
          // Preserve original timestamps
          createdAt: parseDate(activity.createdAt),
          updatedAt: parseDate(activity.updatedAt)
        }
      });
      
      success++;
      
    } catch (error) {
      console.error(`❌ Error migrating activity ${activity._id.$oid}:`, error);
      failed++;
    }
  }
  
  return { success, skipped, failed };
}

function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

async function fullMigration() {
  const startTime = Date.now();
  
  try {
    // Read the activities JSON file
    const activitiesPath = path.join(__dirname, '../data/propbear.activities.json');
    
    if (!fs.existsSync(activitiesPath)) {
      console.error('Please place propbear.activities.json in tooling/scripts/data/');
      console.error(`Looking for file at: ${activitiesPath}`);
      process.exit(1);
    }
    
    console.log('📂 Reading activities file...');
    const activities: OldActivity[] = JSON.parse(fs.readFileSync(activitiesPath, 'utf8'));
    console.log(`📊 Found ${activities.length.toLocaleString()} activities to migrate`);
    
    // Batch processing configuration
    const BATCH_SIZE = 100;
    const totalBatches = Math.ceil(activities.length / BATCH_SIZE);
    
    let totalSuccess = 0;
    let totalSkipped = 0;
    let totalFailed = 0;
    
    console.log(`🚀 Starting migration with batch size: ${BATCH_SIZE}`);
    console.log(`📦 Total batches: ${totalBatches.toLocaleString()}`);
    
    // Process in batches
    for (let i = 0; i < totalBatches; i++) {
      const startIdx = i * BATCH_SIZE;
      const endIdx = Math.min(startIdx + BATCH_SIZE, activities.length);
      const batch = activities.slice(startIdx, endIdx);
      
      const batchStart = Date.now();
      const { success, skipped, failed } = await migrateActivityBatch(batch);
      const batchTime = (Date.now() - batchStart) / 1000;
      
      totalSuccess += success;
      totalSkipped += skipped;
      totalFailed += failed;
      
      // Calculate progress and ETA
      const processed = (i + 1) * BATCH_SIZE;
      const actualProcessed = Math.min(processed, activities.length);
      const progress = (actualProcessed / activities.length) * 100;
      const elapsedTime = (Date.now() - startTime) / 1000;
      const estimatedTotal = (elapsedTime / actualProcessed) * activities.length;
      const eta = estimatedTotal - elapsedTime;
      const rate = actualProcessed / elapsedTime;
      
      console.log(`📊 Batch ${(i + 1).toLocaleString()}/${totalBatches.toLocaleString()} | ` +
        `Progress: ${progress.toFixed(1)}% | ` +
        `✅ ${totalSuccess.toLocaleString()} | ` +
        `⏭️ ${totalSkipped.toLocaleString()} | ` +
        `❌ ${totalFailed.toLocaleString()} | ` +
        `Rate: ${rate.toFixed(1)}/s | ` +
        `ETA: ${formatTime(eta)}`);
      
      // Clear caches periodically to prevent memory issues
      if (i % 100 === 0 && i > 0) {
        organizationCache.clear();
        userCache.clear();
        contactCache.clear();
        propertyCache.clear();
        console.log('🧹 Cleared caches to free memory');
      }
    }
    
    const totalTime = (Date.now() - startTime) / 1000;
    const finalCount = await prisma.activity.count();
    
    console.log(`\n🎉 Full migration completed!`);
    console.log(`⏱️ Total time: ${formatTime(totalTime)}`);
    console.log(`✅ Successfully migrated: ${totalSuccess.toLocaleString()}`);
    console.log(`⏭️ Skipped (missing org/user/duplicate): ${totalSkipped.toLocaleString()}`);
    console.log(`❌ Failed: ${totalFailed.toLocaleString()}`);
    console.log(`📊 Total activities in database: ${finalCount.toLocaleString()}`);
    console.log(`🚀 Average rate: ${(activities.length / totalTime).toFixed(1)} activities/second`);
    
    // Show some statistics
    const activityTypes = await prisma.activity.groupBy({
      by: ['type'],
      _count: { type: true }
    });
    
    console.log(`\n📈 Activity type breakdown:`);
    activityTypes.forEach(({ type, _count }) => {
      console.log(`  ${type}: ${_count.type.toLocaleString()}`);
    });
    
    // Show record type breakdown
    const recordTypes = await prisma.activity.groupBy({
      by: ['recordType'],
      _count: { recordType: true }
    });
    
    console.log(`\n🔗 Record relationship breakdown:`);
    recordTypes.forEach(({ recordType, _count }) => {
      console.log(`  ${recordType || 'unattached'}: ${_count.recordType.toLocaleString()}`);
    });
    
    // Show statistics for call activities with phone numbers preserved
    const callActivitiesWithPhone = await prisma.activity.count({
      where: {
        type: 'call',
        phone: { not: null }
      }
    });
    
    const callActivitiesWithResult = await prisma.activity.count({
      where: {
        type: 'call',
        result: { not: null }
      }
    });
    
    console.log(`\n📞 Call activity preservation:`);
    console.log(`  Call activities with phone preserved: ${callActivitiesWithPhone.toLocaleString()}`);
    console.log(`  Call activities with result preserved: ${callActivitiesWithResult.toLocaleString()}`);
    
  } catch (error) {
    console.error('💥 Full migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

fullMigration(); 