{"dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/auth": "workspace:*", "@repo/utils": "workspace:*", "mongoose": "^8.0.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.13.10", "dotenv-cli": "^8.0.0", "nanoid": "^5.1.2", "tsx": "^4.19.3"}, "name": "@repo/scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "fix-null-credits": "dotenv -c -e ../../.env -- tsx ./src/fix-null-credits.ts", "fix-null-created-by": "dotenv -c -e ../../.env -- tsx ./src/fix-null-created-by.ts", "add-credits": "dotenv -c -e ../../.env -- tsx ./src/add-credits-to-users.ts", "reset-daily-credits": "dotenv -c -e ../../.env -- tsx ./src/reset-daily-credits.ts", "insert-models": "dotenv -c -e ../../.env -- tsx ./src/insert-models.ts", "migrate:test": "dotenv -c -e ../../.env -- tsx ./src/test-migration-single-user.ts", "migrate:full": "dotenv -c -e ../../.env -- tsx ./src/migrate-users-from-old-db.ts", "create-forwarding-configs": "dotenv -c -e ../../.env -- tsx ./src/create-forwarding-configs.ts", "migrate-watermark": "dotenv -c -e ../../.env -- tsx ./src/migrate-email-watermark.ts", "migrate-propbear": "dotenv -c -e ../../.env -- tsx ./src/migrate-propbear-data.ts", "test-contacts": "dotenv -c -e ../../.env -- tsx ./src/migrate-contacts-test.ts", "migrate-contacts": "dotenv -c -e ../../.env -- tsx src/migrate-contacts-full.ts", "test-activities": "dotenv -c -e ../../.env -- tsx ./src/migrate-activities-test.ts", "migrate-activities": "dotenv -c -e ../../.env -- tsx src/migrate-activities-full.ts", "migrate-object-relationships": "dotenv -c -e ../../.env -- tsx src/migrate-to-object-relationships.ts", "type-check": "tsc --noEmit", "migrate-users": "dotenv -c -e ../../.env -- tsx src/migrate-propbear-data.ts", "migrate-contacts-test": "dotenv -c -e ../../.env -- tsx src/migrate-contacts-test.ts", "migrate-activities-test": "dotenv -c -e ../../.env -- tsx src/migrate-activities-test.ts", "migrate-properties-test": "dotenv -c -e ../../.env -- tsx src/migrate-properties-test.ts", "migrate-properties": "dotenv -c -e ../../.env -- tsx src/migrate-properties-full.ts", "fix-schema": "dotenv -c -e ../../.env -- tsx src/fix-schema-auto-ids.ts"}, "version": "0.0.0"}